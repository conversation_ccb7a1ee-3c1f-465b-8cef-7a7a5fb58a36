# Fast OCR

一个基于 Electron + React + TypeScript + Vite 的现代化 OCR（光学字符识别）桌面应用程序。

## 🚀 技术栈

- **前端框架**: React 18 + TypeScript
- **桌面应用**: Electron 37
- **构建工具**: Vite 7
- **开发工具**: TypeScript 5.8
- **打包工具**: Electron Forge

## 📁 项目结构

```
fast-ocr/
├── src/
│   ├── main/                 # Electron 主进程
│   │   ├── index.ts         # 主进程入口文件
│   │   └── tsconfig.json    # 主进程 TypeScript 配置
│   ├── preload/             # 预加载脚本
│   │   ├── index.ts         # 预加载脚本入口
│   │   └── tsconfig.json    # 预加载脚本 TypeScript 配置
│   ├── renderer/            # React 渲染进程
│   │   ├── components/      # React 组件
│   │   │   ├── TitleBar.tsx # 自定义标题栏
│   │   │   ├── MainContent.tsx # 主内容区域
│   │   │   └── StatusBar.tsx # 状态栏
│   │   ├── App.tsx          # 主应用组件
│   │   ├── main.tsx         # React 入口文件
│   │   ├── index.html       # HTML 模板
│   │   ├── index.css        # 全局样式
│   │   └── tsconfig.json    # 渲染进程 TypeScript 配置
│   └── shared/              # 共享代码
│       ├── types/           # 类型定义
│       │   └── index.ts     # 共享类型
│       └── utils/           # 工具函数
│           └── index.ts     # 共享工具函数
├── public/                  # 静态资源
├── dist-electron/           # 构建输出目录
│   ├── main/               # 主进程构建输出
│   ├── preload/            # 预加载脚本构建输出
│   └── renderer/           # 渲染进程构建输出
├── vite.config.ts          # Vite 配置
├── tsconfig.json           # 根 TypeScript 配置
├── forge.config.js         # Electron Forge 配置
└── package.json            # 项目配置
```

## 🛠️ 开发指南

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建应用

```bash
npm run build
```

### 启动应用

```bash
npm start
```

### 打包应用

```bash
npm run package
```

### 制作安装包

```bash
npm run make
```

### 清理构建文件

```bash
npm run clean
```

## 🏗️ 架构特点

### 前后端分离
- **主进程 (Main Process)**: 负责应用生命周期管理、窗口创建、系统 API 调用
- **渲染进程 (Renderer Process)**: 运行 React 应用，负责用户界面
- **预加载脚本 (Preload Script)**: 安全地暴露 Electron API 给渲染进程

### 类型安全
- 全项目使用 TypeScript，提供完整的类型检查
- 共享类型定义，确保前后端数据结构一致
- 严格的 TypeScript 配置，提高代码质量

### 现代化构建
- 使用 Vite 作为前端构建工具，提供快速的开发体验
- 支持热重载和快速构建
- 优化的生产构建，包含代码分割和压缩

### 安全性
- 启用上下文隔离 (Context Isolation)
- 禁用 Node.js 集成在渲染进程中
- 通过预加载脚本安全地暴露 API

## 🔧 配置说明

### TypeScript 配置
- 根目录 `tsconfig.json`: 项目级别的 TypeScript 配置
- 各模块独立的 `tsconfig.json`: 针对不同模块的特定配置

### Vite 配置
- 支持 React 和 TypeScript
- 配置路径别名，简化导入
- 集成 Electron 插件

### Electron Forge 配置
- 支持多平台打包
- 配置应用图标和元数据
- 自动代码签名和更新

## 📦 构建流程

1. **渲染进程构建**: 使用 Vite 构建 React 应用
2. **主进程构建**: 使用 TypeScript 编译器构建主进程代码
3. **预加载脚本构建**: 使用 TypeScript 编译器构建预加载脚本
4. **应用打包**: 使用 Electron Forge 打包最终应用

## 🎯 功能特性

- ✅ 现代化的用户界面
- ✅ 跨平台支持 (Windows, macOS, Linux)
- ✅ 自定义标题栏
- ✅ 文件对话框集成
- ✅ 应用状态管理
- ✅ 开发者工具集成
- ✅ 热重载开发体验

## 🔮 未来计划

- [ ] OCR 功能集成
- [ ] 多语言支持
- [ ] 主题切换
- [ ] 插件系统
- [ ] 自动更新

## 📄 许可证

MIT License

## 👨‍💻 开发者

jf - [https://gitlab.hunantianxing.com/](https://gitlab.hunantianxing.com/)
